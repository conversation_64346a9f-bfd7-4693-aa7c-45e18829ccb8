"""
合约分析器
负责合约交易数据的解析、处理和异常检测
"""

import pandas as pd
import numpy as np
import time
from typing import Dict, List, Any, Optional
import uuid
import logging
from datetime import datetime, timedelta

# 配置日志记录
logger = logging.getLogger(__name__)

class CTContractAnalyzer:
    """合约分析模块，负责处理上传的合约数据并进行异常检测"""
    
    def __init__(self, task_id=None):
        """初始化合约分析器"""
        # 任务ID，用于关联分析结果
        self.task_id = task_id or ""
        
        # 新增：基于positionId的优化器
        self.position_optimizer = None
        
        # 配置开关：是否启用基于positionId的优化
        self.enable_position_optimization = True
        
        # 字段映射表：记录所有原始字段及其说明，便于后期算法查询调用
        self.fields_mapping = {
            # 基本标识字段
            'id': {'description': '记录 ID', 'detail': '唯一标识每条数据记录，用于数据库索引和数据关联'},
            'uid': {'description': '用户 ID', 'detail': '操作方用户唯一标识（如交易用户 ID）'},
            'member_id': {'description': '会员 ID', 'detail': '会员体系中的用户标识，可能与业务层级或权限相关'},
            'digital_id': {'description': '数字 ID', 'detail': '用户的数字资产账户 ID 或其他数字化标识'},
            
            # 关联用户字段
            'parent_uid': {'description': '上级用户 ID', 'detail': '层级关系中的上级用户（如代理、推荐人），用于分润或权限继承'},
            'parent_member_id': {'description': '上级会员 ID', 'detail': '上级会员的标识，对应parent_uid的会员体系信息'},
            'parent_digital_id': {'description': '上级数字 ID', 'detail': '上级用户的数字资产账户 ID'},
            'opponent_uid': {'description': '对手方用户 ID', 'detail': '交易对手方用户（如买卖方、合约对手方）的唯一标识'},
            'opponent_member_id': {'description': '对手方会员 ID', 'detail': '对手方用户的会员信息'},
            'opponent_digital_id': {'description': '对手方数字 ID', 'detail': '对手方的数字资产账户 ID'},
            'opponent_parent_uid': {'description': '对手方上级用户 ID', 'detail': '对手方用户的上级层级用户 ID'},
            'opponent_parent_member_id': {'description': '对手方上级会员 ID', 'detail': '对手方上级会员的标识'},
            'opponent_parent_digital_id': {'description': '对手方上级数字 ID', 'detail': '对手方上级用户的数字资产账户 ID'},
            
            # 合约信息字段
            'contract_id': {'description': '合约 ID', 'detail': '交易合约唯一标识（如期货合约、期权合约）'},
            'contract_name': {'description': '合约名称', 'detail': '合约的名称或类型（如 "BTC/USDT 永续合约"）'},
            'base_coin': {'description': '基础币种', 'detail': '交易对中的基础货币（如 BTC）'},
            'quote_coin': {'description': '报价币种', 'detail': '交易对中的报价货币（如 USDT）'},
            'settle_coin': {'description': '结算币种', 'detail': '合约结算使用的货币（如 USDT）'},
            'contract_size': {'description': '合约规模', 'detail': '单个合约的面值或规模（如每张合约对应 100 美元）'},
            
            # 订单和仓位字段
            'position_id': {'description': '仓位 ID', 'detail': '用户持仓仓位的唯一标识', 'usage': '用于准确分析持仓时间，常持刷量检测核心字段'},
            'order_id': {'description': '订单 ID', 'detail': '用户订单的唯一标识'},
            'opponent_order_id': {'description': '对手方订单 ID', 'detail': '对手方订单的唯一标识，用于匹配成交'},
            
            # 交易方向和类型字段
            'side': {'description': '交易方向（英文）', 'detail': '订单方向（如buy/sell，long/short）', 'usage': '常持刷量检测使用，标识开平仓方向'},
            'side_cn': {'description': '交易方向（中文）', 'detail': '订单方向中文描述（如 "买入""卖出""做多""做空"）', 'usage': '常持刷量检测使用，映射为side编码(1=开多,4=平多,3=开空,2=平空)'},
            'category': {'description': '交易类型（英文）', 'detail': '订单类型分类（如limit/market，swap/option）'},
            'category_cn': {'description': '交易类型（中文）', 'detail': '订单类型中文描述（如 "限价单""市价单""现货""期权"）'},
            'order_type': {'description': '订单类型（英文）', 'detail': '更细分的订单类型（如open/close，stop/take_profit）'},
            'order_type_cn': {'description': '订单类型（中文）', 'detail': '细分订单类型中文描述（如 "开仓""平仓""止损""止盈"）'},
            'open_type': {'description': '开仓类型（英文）', 'detail': '开仓方式（如market_open/limit_open）'},
            'open_type_cn': {'description': '开仓类型（中文）', 'detail': '开仓方式中文描述（如 "市价开仓""限价开仓"）'},
            
            # 保证金和费用字段
            'frozen_margin': {'description': '冻结保证金', 'detail': '订单冻结的保证金金额（未成交部分）'},
            'used_margin': {'description': '已用保证金', 'detail': '成交后占用的保证金金额'},
            'maker_fee': {'description': '挂单手续费', 'detail': '作为流动性提供者（Maker）收取/支付的手续费'},
            'taker_fee': {'description': '吃单手续费', 'detail': '作为流动性获取者（Taker）收取/支付的手续费'},
            'profit': {'description': '盈亏金额', 'detail': '订单或仓位的盈亏数值（可能为正/负）', 'usage': '循环对敲检测使用，计算盈亏波动'},
            
            # 订单状态字段
            'state': {'description': '订单状态（英文）', 'detail': '订单状态（如pending/filled/cancelled）'},
            'state_en': {'description': '状态英文码', 'detail': '英文状态码（可能与state重复，需确认字段用途）'},
            'state_cn': {'description': '订单状态（中文）', 'detail': '订单状态中文描述（如 "待成交""已成交""已撤销"）'},
            'external_oid': {'description': '外部订单号', 'detail': '第三方系统的订单编号（用于跨系统对账）'},
            
            # 时间字段
            'order_create_time': {'description': '订单创建时间', 'detail': '订单生成的时间戳（精确到秒或毫秒）', 'usage': '常持刷量检测核心字段，用于计算持仓时间'},
            'create_time': {'description': '记录创建时间', 'detail': '数据记录在系统中的生成时间戳'},
            
            # 价格和数量字段
            'order_price': {'description': '订单价格', 'detail': '订单指定的价格（如限价单价格）'},
            'deal_price': {'description': '成交价格', 'detail': '实际成交的价格（可能与订单价格不同，如市价单滑点）'},
            'deal_avg_price': {'description': '成交均价', 'detail': '多次成交的平均价格'},
            'leverage': {'description': '杠杆倍数', 'detail': '合约交易使用的杠杆比例（如 10 倍、100 倍）'},
            'order_vol': {'description': '订单数量', 'detail': '订单委托的数量（如买入 100 张合约）'},
            'deal_vol': {'description': '成交数量', 'detail': '实际成交的数量'},
            'deal_vol_usdt': {'description': '成交数量（USDT 换算）', 'detail': '以 USDT 计价的成交数量（用于统一计量）', 'usage': '常持刷量检测核心字段，计算交易量比例'},
            'remain_vol': {'description': '剩余数量', 'detail': '未成交的剩余订单数量'},
            
            # 手续费字段
            'fee_currency': {'description': '手续费币种', 'detail': '手续费收取的货币类型（如 BTC、USDT）'},
            'fee': {'description': '手续费金额', 'detail': '手续费的数值（以fee_currency计价）'},
            'fee_usdt': {'description': '手续费（USDT 换算）', 'detail': '手续费以 USDT 计价的金额（便于财务统计）'},
            'sys_fee': {'description': '系统费用', 'detail': '系统收取的其他费用（如平台费、清算费）'},
            'sys_fee_usdt': {'description': '系统费用（USDT 换算）', 'detail': '系统费用以 USDT 计价的金额'},
            
            # 其他字段
            'is_taker': {'description': '是否为吃单方', 'detail': '标识订单是否为吃单（1为是，0为否），影响手续费类型'},
            'liquidity': {'description': '流动性标识', 'detail': '订单对市场流动性的影响（如high/low流动性）'},
            'is_self': {'description': '是否自成交', 'detail': '标识是否为自成交（防止操纵市场），1为是，0为否', 'usage': '可直接用于对敲检测'},
            'display_name': {'description': '显示名称', 'detail': '用户或合约的显示名称（如昵称、合约简称）'},
            'origin': {'description': '业务来源', 'detail': '数据来源标识（如web/app/api）'},
            'dt': {'description': '日期分区', 'detail': '数据按日期分区的字段（如2025-05-27，用于数据存储和查询）'},
            
            # 驼峰命名字段映射（兼容前端格式）
            'memberId': {'description': '用户 ID', 'detail': '等同于member_id', 'map_to': 'member_id'},
            'contractName': {'description': '合约名称', 'detail': '等同于contract_name', 'map_to': 'contract_name'},
            'dealVolUsdt': {'description': '成交金额(USDT)', 'detail': '等同于deal_vol_usdt', 'map_to': 'deal_vol_usdt'},
            'dealVol': {'description': '成交数量', 'detail': '等同于deal_vol', 'map_to': 'deal_vol'},
            'dealAvgPrice': {'description': '成交均价', 'detail': '等同于deal_avg_price', 'map_to': 'deal_avg_price'},
            'createTime': {'description': '创建时间', 'detail': '时间戳，转换为order_create_time', 'map_to': 'order_create_time'},
            'sideCn': {'description': '交易方向', 'detail': '中文交易方向，映射为side', 'map_to': 'side'},
            'positionId': {'description': '仓位ID', 'detail': '等同于position_id', 'map_to': 'position_id'},
        }
        
        # 算法依赖字段映射：记录各算法依赖的核心字段
        self.algorithm_fields = {
            'high_frequency_trading': ['member_id', 'contract_name', 'minute_floor', 'deal_vol_usdt'],
            'suspected_wash_trading': ['member_id', 'contract_name', 'minute_floor', 'side', 'deal_vol_usdt', 'order_create_time'],
            'funding_rate_arbitrage': ['member_id', 'contract_name', 'hour', 'minute', 'profit', 'deal_vol_usdt'],
            'regular_brush_trading': ['member_id', 'contract_name', 'side', 'deal_vol_usdt', 'order_create_time', 'position_id']
        }
        
        # 计算字段映射：记录需要计算的字段及其来源
        self.calculated_fields = {
            'minute_floor': {'source': 'order_create_time', 'method': 'dt.floor("T")'},
            'hour': {'source': 'order_create_time', 'method': 'dt.hour'},
            'minute': {'source': 'order_create_time', 'method': 'dt.minute'},
            'date': {'source': 'order_create_time', 'method': 'dt.date'},
            'short_positions_count': {'description': '持仓时间小于1分钟的次数', 'algorithm': 'regular_brush_trading'},
            'total_duration': {'description': '从首次开仓到所有持仓清零的总时长(秒)', 'algorithm': 'regular_brush_trading'},
            'volume_ratio': {'description': '后续开仓总量与首单开仓量的比例', 'algorithm': 'regular_brush_trading'},
            'trade_frequency': {'description': '交易频率(次/小时)', 'algorithm': 'regular_brush_trading'}
        }
        
        # Side字段中文到数字的映射
        self.side_mapping = {
            'buy': 1,        # 开多
            'b': 1,
            'long': 1,
            '开多': 1,
            '多': 1,
            'sell': 3,       # 开空
            's': 3,
            'short': 3,
            '开空': 3,
            '空': 3,
            '平多': 4,       # 平多
            '平long': 4,
            'cover_long': 4,
            '平空': 2,       # 平空
            '平short': 2,
            'cover_short': 2
        }
    
    def process_contract_data(self, df: pd.DataFrame, progress_callback=None) -> List[Dict[str, Any]]:
        """
        处理合约数据进行风险分析 - 优化版本（消除重复检测）
        
        参数:
            df: 合约交易数据DataFrame
            progress_callback: 进度回调函数
            
        返回:
            检测结果列表
        """
        if df.empty:
            logger.warning("输入数据为空，跳过分析")
            return []
        
        # logger.info(f"开始合约风险分析，数据量: {len(df)} 条记录")
        
        # 进度回调：数据预处理
        if progress_callback:
            progress_callback({
                'stage': '数据预处理',
                'percentage': 10,
                'message': '正在标准化数据字段...'
            })
        
        # 数据标准化（一次性处理）
        df = self._standardize_fields(df)
        
        # 进度回调：检测器初始化
        if progress_callback:
            progress_callback({
                'stage': '检测器初始化',
                'percentage': 20,
                'message': '正在初始化新一代检测器...'
            })
        
        # 优先尝试使用基于positionId的优化器
        if self.enable_position_optimization:
                    return self._process_contract_data_optimized(df, progress_callback)
        
        from ..algorithms.wash_trading.detector import NextGenWashTradingDetector
        detector = NextGenWashTradingDetector()
        
        # 进度回调：开始检测
        if progress_callback:
            progress_callback({
                'stage': '风险检测',
                'percentage': 30,
                'message': '正在执行集成风险检测...'
            })
        
        # 创建进度回调适配器
        def integrated_progress_callback(progress_info):
            # 将检测器的进度映射到总进度的30%-95%范围
            detector_percentage = progress_info.get('percentage', 0)
            total_percentage = 30 + (detector_percentage / 100) * 65
            
            if progress_callback:
                progress_callback({
                    'stage': progress_info.get('stage', '风险检测'),
                    'percentage': min(total_percentage, 95),
                    'message': progress_info.get('message', '正在进行风险检测...'),
                    'details': progress_info
                })
        
        # 执行集成检测（包含数据验证、仓位画像、质量验证、结果过滤）
        detection_result = detector.detect(df, progress_callback=integrated_progress_callback)
        
        if detection_result.get('status') == 'success':
            detected_pairs = detection_result.get('detected_pairs', [])
            # logger.info(f"✅ 新一代检测器完成，发现 {len(detected_pairs)} 个高质量风险点")
            
            # 进度回调：结果格式化
            if progress_callback:
                progress_callback({
                    'stage': '结果格式化',
                    'percentage': 95,
                    'message': f'正在格式化 {len(detected_pairs)} 个检测结果...'
                })
            
            # 格式化结果（确保与现有接口兼容）
            formatted_results = self._format_results(detected_pairs)
            
            # 记录质量统计
            quality_stats = detection_result.get('statistics', {}).get('quality_stats', {})
            logger.info(f"质量统计: 高质量={quality_stats.get('high_quality', 0)}, "
                       f"中等质量={quality_stats.get('medium_quality', 0)}, "
                       f"低质量={quality_stats.get('low_quality', 0)}, "
                       f"平均评分={quality_stats.get('average_quality_score', 0.0):.2f}")
            
            # 进度回调：完成
            if progress_callback:
                progress_callback({
                    'stage': '分析完成',
                    'percentage': 100,
                    'message': f'分析完成，发现 {len(formatted_results)} 个高质量风险点'
                })
            
            return formatted_results
        else:
            error_msg = detection_result.get('message', '新检测器执行失败')
            logger.error(f"❌ 新检测器执行失败: {error_msg}")
            raise RuntimeError(error_msg)
    
    def _standardize_fields(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        标准化数据字段，确保与检测器兼容
        
        参数:
            df: 原始数据框
            
        返回:
            标准化后的数据框
        """
        # 创建副本避免修改原始数据
        result_df = df.copy()
        
        # 处理非标量值，确保所有列都是一维的
        for col in result_df.columns:
            if result_df[col].apply(lambda x: isinstance(x, (list, dict, set, tuple))).any():
                result_df.loc[:, col] = result_df[col].apply(lambda x: str(x) if isinstance(x, (list, dict, set, tuple)) else x)
        
        # 应用驼峰命名到下划线命名的映射
        for camel_col, info in self.fields_mapping.items():
            if 'map_to' in info and camel_col in result_df.columns:
                target_col = info['map_to']
                
                # 如果目标列不存在，直接映射
                if target_col not in result_df.columns:
                    result_df.loc[:, target_col] = result_df[camel_col]
                
                # 特殊处理sideCn字段，需要转换为数字
                if camel_col == 'sideCn' and target_col == 'side':
                    result_df = self._normalize_side_values(result_df, camel_col)
        
        # 处理时间字段
        if 'createTime' in result_df.columns:
            result_df = self._process_time_fields(result_df)
        
        # 生成缺失的profit字段
        if 'profit' not in result_df.columns:
            result_df = self._generate_profit_field(result_df)
        
        # 确保ID字段为字符串类型，防止前导零丢失
        for id_field in ['member_id', 'position_id']:
            if id_field in result_df.columns:
                result_df.loc[:, id_field] = result_df[id_field].astype(str)
        
        # 添加时间相关辅助字段
        if 'order_create_time' in result_df.columns:
            result_df = self._add_time_derived_fields(result_df)
        
        # 添加timestamp字段（多个算法需要）
        if 'order_create_time' in result_df.columns and 'timestamp' not in result_df.columns:
            result_df.loc[:, 'timestamp'] = result_df['order_create_time']
        
        return result_df
    
    def _normalize_side_values(self, df: pd.DataFrame, source_col: str = 'sideCn') -> pd.DataFrame:
        """
        规范化交易方向值为数字格式
        1=开多, 2=平空, 3=开空, 4=平多
        
        参数:
            df: 输入DataFrame
            source_col: 源字段名，默认为'sideCn'
            
        返回:
            规范化后的DataFrame
        """
        if df is None or df.empty or source_col not in df.columns:
            return df
            
        # 创建副本避免警告
        result_df = df.copy()
        
        # 应用映射
        result_df.loc[:, 'side'] = result_df[source_col].map(
            lambda x: self.side_mapping.get(str(x).strip(), x) if pd.notna(x) else x
        )
        
        # 尝试转换为数字类型
        result_df.loc[:, 'side'] = pd.to_numeric(result_df['side'], errors='coerce')
        
        return result_df
    
    def _process_time_fields(self, df: pd.DataFrame) -> pd.DataFrame:
        """处理时间字段 - 支持多种时间字段名称"""
        # 创建副本避免警告
        result_df = df.copy()
        
        # 检查可能的时间字段名称
        time_field = None
        if 'createTime' in df.columns:
            time_field = 'createTime'
        elif 'timestamp' in df.columns:
            time_field = 'timestamp'
        elif 'order_create_time' in df.columns:
            time_field = 'order_create_time'
        
        if time_field is None:
            return result_df
        
        try:
            # 尝试将时间字段转换为datetime
            time_data = result_df[time_field]
            
            # 如果已经是datetime类型，直接使用
            if pd.api.types.is_datetime64_any_dtype(time_data):
                result_df.loc[:, 'order_create_time'] = time_data
            else:
                # 尝试多种转换方式
                try:
                    # 方式1: 直接转换字符串格式的时间
                    converted_time = pd.to_datetime(time_data, errors='coerce')
                    if converted_time.notna().any():
                        result_df.loc[:, 'order_create_time'] = converted_time
                    else:
                        raise ValueError("字符串转换失败")
                except Exception:
                    try:
                        # 方式2: 尝试作为毫秒时间戳处理
                        numeric_time = pd.to_numeric(time_data, errors='coerce')
                        converted_time = pd.to_datetime(numeric_time, unit='ms', errors='coerce')
                        if converted_time.notna().any():
                            result_df.loc[:, 'order_create_time'] = converted_time
                        else:
                            raise ValueError("毫秒时间戳转换失败")
                    except Exception:
                        try:
                            # 方式3: 尝试秒级时间戳
                            numeric_time = pd.to_numeric(time_data, errors='coerce')
                            converted_time = pd.to_datetime(numeric_time, unit='s', errors='coerce')
                            if converted_time.notna().any():
                                result_df.loc[:, 'order_create_time'] = converted_time
                            else:
                                # 如果所有方式都失败，设置为默认值
                                result_df.loc[:, 'order_create_time'] = pd.NaT
                        except Exception:
                            # 如果失败，创建一个默认值
                            result_df.loc[:, 'order_create_time'] = pd.NaT
                            
        except Exception as e:
            logger.warning(f"时间字段处理失败: {e}")
            result_df.loc[:, 'order_create_time'] = pd.NaT
        
        return result_df
    
    def _generate_profit_field(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        处理profit字段 - 严格模式（不生成模拟数据）
        如果缺少真实profit数据，直接抛出异常
        """
        # 创建副本避免警告
        result_df = df.copy()
        
        # 关键修改：严格检查profit字段的真实性
        if 'profit' in result_df.columns:
            # 检查profit字段是否有有效数据
            valid_profit_count = result_df['profit'].notna().sum()
            if valid_profit_count > 0:
                logger.info(f"✅ 使用原始profit字段，有效数据: {valid_profit_count} 条")
                # 将NaN值填充为0，但保留原始profit数据
                result_df.loc[:, 'profit'] = result_df['profit'].fillna(0)
                return result_df
        
     
        
        # 检查是否可以从其他字段计算
        if 'deal_vol_usdt' in result_df.columns and 'deal_avg_price' in result_df.columns:
            logger.error("❌ 检测到成交数据，但缺少价格差异信息无法计算真实盈亏")
        
        raise ValueError(
            "❌ 缺少必要的profit字段，无法进行风险分析！\n"
            "请确保数据源包含真实的交易盈亏信息。\n"
            "如果您的数据源没有profit字段，请联系技术团队添加相关字段。"
        )
    
    def _add_time_derived_fields(self, df: pd.DataFrame) -> pd.DataFrame:
        """添加时间相关的派生字段"""
        if 'order_create_time' not in df.columns:
            return df
            
        # 创建副本避免修改原始数据
        result_df = df.copy()
        
        try:
            # 尝试多种转换方式
            if not pd.api.types.is_datetime64_any_dtype(result_df['order_create_time']):
                # 方式1: 直接转换字符串格式的时间
                try:
                    converted_time = pd.to_datetime(result_df['order_create_time'], errors='coerce')
                    if converted_time.notna().any():
                        result_df = result_df.copy()  # 创建新副本
                        result_df['order_create_time'] = converted_time
                    else:
                        raise ValueError("转换结果全为NaT")
                except Exception:
                    # 方式2: 尝试作为时间戳处理
                    try:
                        numeric_time = pd.to_numeric(result_df['order_create_time'], errors='coerce')
                        converted_time = pd.to_datetime(numeric_time, unit='ms', errors='coerce')
                        if converted_time.notna().any():
                            result_df = result_df.copy()  # 创建新副本
                            result_df['order_create_time'] = converted_time
                        else:
                            raise ValueError("时间戳转换结果全为NaT")
                    except Exception:
                        # 方式3: 尝试秒级时间戳
                        try:
                            numeric_time = pd.to_numeric(result_df['order_create_time'], errors='coerce')
                            converted_time = pd.to_datetime(numeric_time, unit='s', errors='coerce')
                            if converted_time.notna().any():
                                result_df = result_df.copy()  # 创建新副本
                                result_df['order_create_time'] = converted_time
                            else:
                                return result_df
                        except Exception:
                            return result_df
            
            # 确保order_create_time列是有效的日期时间
            valid_time_mask = result_df['order_create_time'].notna()
            
            if not valid_time_mask.any():
                return result_df
            
            # 只对有效时间的行进行处理
            # 创建时间派生字段
            result_df.loc[valid_time_mask, 'hour'] = result_df.loc[valid_time_mask, 'order_create_time'].dt.hour
            result_df.loc[valid_time_mask, 'minute'] = result_df.loc[valid_time_mask, 'order_create_time'].dt.minute
            result_df.loc[valid_time_mask, 'date'] = result_df.loc[valid_time_mask, 'order_create_time'].dt.date
            
            try:
                result_df.loc[valid_time_mask, 'minute_floor'] = result_df.loc[valid_time_mask, 'order_create_time'].dt.floor('T')
            except Exception:
                # 备选方案：使用字符串格式化
                try:
                    result_df.loc[valid_time_mask, 'minute_str'] = result_df.loc[valid_time_mask, 'order_create_time'].dt.strftime('%Y-%m-%d %H:%M')
                except Exception:
                    pass
            
        except Exception:
            pass
        
        return result_df
    
    def _format_results(self, detection_results: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        格式化检测结果为标准输出格式
        
        参数:
            detection_results: 检测结果列表
            
        返回:
            格式化后的结果列表
        """
        formatted_results = []
        for result in detection_results:
            # 确保基础字段存在，与旧版本和前端期望的格式保持一致
            formatted_result = {
                'detection_type': result.get('detection_type', ''),
                'memberId': result.get('memberId', result.get('member_id', '')),
                'contractName': result.get('contractName', result.get('contract_name', '')),
                'abnormal_volume': float(result.get('abnormal_volume', result.get('abnormalVolume', 0))),
                'abnormalVolume': float(result.get('abnormal_volume', result.get('abnormalVolume', 0))),  # 兼容前端
                'reason': result.get('reason', ''),
                'severity': result.get('severity', result.get('risk_level', '中')),
                'minute': result.get('minute', result.get('timeRange', '')),  # 保持时间字段，与旧版本兼容
                'timeRange': result.get('timeRange', result.get('time_range', result.get('minute', ''))),  # 新版本字段
                # 保留原始结果中的其他字段
                **{k: v for k, v in result.items() if k not in [
                    'detection_type', 'memberId', 'contractName', 'abnormal_volume', 
                    'abnormalVolume', 'reason', 'severity', 'timeRange', 'minute'
                ]}
            }
            formatted_results.append(formatted_result)
        
        return formatted_results     
    def _process_contract_data_optimized(self, df: pd.DataFrame, progress_callback=None) -> List[Dict[str, Any]]:
        """
        基于positionId优化的合约数据处理方法
        使用完整订单生命周期分析，提升检测准确性和效率
        """
        # logger.info(f"使用优化版本处理合约数据，数据量: {len(df)} 条记录")
        
        # 进度回调：初始化优化器
        if progress_callback:
            progress_callback({
                'stage': '初始化优化器',
                'percentage': 15,
                'message': '正在初始化基于positionId的优化器...'
            })
        
        # 初始化优化器和用户行为分析器
        try:
            from ..optimizers.position_based_optimizer import PositionBasedOptimizer
            from modules.user_analysis.services.user_behavior_analyzer import UserBehaviorAnalyzer
            self.position_optimizer = PositionBasedOptimizer()
            self.behavior_analyzer = UserBehaviorAnalyzer()
        except ImportError as e:
            logger.error(f"无法导入优化器或用户行为分析器: {str(e)}")
            raise
        
        # 进度回调：构建完整订单
        if progress_callback:
            progress_callback({
                'stage': '构建完整订单',
                'percentage': 30,
                'message': '正在按positionId构建完整订单画像...'
            })
        
        # 构建完整订单画像
        complete_positions = self.position_optimizer.build_complete_positions(df)
        if not complete_positions:
            logger.warning("未能构建任何完整订单，可能数据格式不符合要求")
            return []
        
        # logger.info(f"成功构建 {len(complete_positions)} 个完整订单")
        
        # 进度回调：执行检测
        if progress_callback:
            progress_callback({
                'stage': '执行优化检测',
                'percentage': 60,
                'message': '正在执行基于完整订单的风险检测...'
            })
        
        # 获取综合分析结果
        analysis_result = self.position_optimizer.get_comprehensive_analysis()
        all_results = analysis_result['results']
        statistics = analysis_result['statistics']
        
        # 新增：保存对敲详细信息
        if progress_callback:
            progress_callback({
                'stage': '保存详细信息',
                'percentage': 75,
                'message': '正在保存对敲详细交易对信息...'
            })
        
        # 保存对敲详细信息
        wash_trading_results = [r for r in all_results if r.get('detection_type') == 'wash_trading']
        if wash_trading_results:
            self._save_wash_trading_details(wash_trading_results)
        
        # 🚀 新增：保存持仓分析数据到position_analysis表（关键修复）
        if progress_callback:
            progress_callback({
                'stage': '保存持仓数据',
                'percentage': 80,
                'message': '正在保存持仓分析数据到数据库...'
            })
        
        # 转换complete_positions为position_data_list格式并保存
        position_data_list = self._convert_complete_positions_to_position_data(complete_positions)
        if position_data_list:
            self._save_position_analysis_data(position_data_list)
            logger.info(f"✅ 成功保存 {len(position_data_list)} 条持仓分析数据到position_analysis表")
        else:
            logger.warning("⚠️ 没有持仓数据需要保存")
        
        # 🚀 新增：触发用户行为分析并保存到user_trading_profiles表
        if position_data_list:  # 只有在有持仓数据时才执行用户行为分析
            if progress_callback:
                progress_callback({
                    'stage': '用户行为分析',
                    'percentage': 82,
                    'message': '正在执行用户行为分析...'
                })
            
            # 按用户分组持仓数据并执行行为分析
            user_positions = {}
            for position_data in position_data_list:
                user_id = position_data.member_id
                if user_id not in user_positions:
                    user_positions[user_id] = []
                user_positions[user_id].append(position_data)
            
            # 对每个用户执行行为分析 - 显示累积进度
            total_users = len(user_positions)
            behavior_analysis_count = 0
            insufficient_data_count = 0
            failed_analysis_count = 0
            processed_count = 0
            
            # 根据用户数量动态调整进度输出频率
            if total_users <= 50:
                progress_interval = 10
            elif total_users <= 200:
                progress_interval = 25
            else:
                progress_interval = 50
            
            # logger.info(f"🚀 开始用户行为分析，共 {total_users} 个用户...")
            
            for user_id, positions in user_positions.items():
                processed_count += 1
                
                if len(positions) >= 3:  # 筛选条件：至少3笔交易才进行分析
                    try:
                        analysis_result = self.behavior_analyzer.analyze_user_behavior(user_id, positions)
                        self._save_user_behavior_result(user_id, analysis_result)
                        behavior_analysis_count += 1
                    except Exception as e:
                        failed_analysis_count += 1
                        # 只记录错误到日志，不输出到控制台
                        logger.error(f"用户 {user_id} 行为分析失败: {str(e)}")
                        continue
                else:
                    insufficient_data_count += 1
                
                # 根据用户数量动态调整进度输出频率 - 已注释减少日志输出
                # if processed_count % progress_interval == 0 or processed_count == total_users:
                #     progress_percentage = (processed_count / total_users) * 100
                #     logger.info(f"📊 用户行为分析进度: {processed_count}/{total_users} ({progress_percentage:.1f}%) "
                #                f"- 成功={behavior_analysis_count}, 数据不足={insufficient_data_count}, 失败={failed_analysis_count}")
            
            # 输出最终汇总统计信息 - 保留重要统计
            logger.warning(f"🎯 用户行为分析完成: 总用户数={total_users}, "
                       f"成功分析={behavior_analysis_count}, "
                       f"数据不足(<3笔)={insufficient_data_count}, "
                       f"分析失败={failed_analysis_count}")
        else:
            logger.warning("⚠️ 没有持仓数据，跳过用户行为分析")
        
        # 进度回调：格式化结果
        if progress_callback:
            progress_callback({
                'stage': '格式化结果',
                'percentage': 85,
                'message': '正在格式化检测结果...'
            })
        
        # 格式化结果以保持向后兼容
        formatted_results = self._format_optimized_results(all_results)
        
        # 添加统计信息到日志 - 保留重要统计
        logger.warning(f"优化检测完成 - 总订单: {statistics['total_positions']}, "
                   f"对敲: {statistics['wash_trading_pairs']}, "
                   f"高频: {statistics['high_frequency_users']}, "
                   f"套利: {statistics['arbitrage_users']}")
        
        # 进度回调：完成
        if progress_callback:
            progress_callback({
                'stage': '检测完成',
                'percentage': 100,
                'message': f'优化检测完成，发现 {len(formatted_results)} 个异常'
            })
        
        # 返回结果和完整持仓数据（用于用户行为分析）
        return {
            'results': formatted_results,
            'complete_positions': complete_positions,
            'statistics': statistics
        }
    
    def _convert_complete_positions_to_position_data(self, complete_positions: Dict) -> List:
        """
        将CompletePosition对象转换为position_analysis表的数据格式
        """
        try:
            from modules.user_analysis.models.user_behavior_models import PositionData
            from datetime import datetime
            import pandas as pd
            
            position_data_list = []
            converted_count = 0
            skipped_count = 0
            
            logger.info(f"开始转换 {len(complete_positions)} 个完整持仓数据...")
            
            for position_id, complete_position in complete_positions.items():
                try:
                    # 安全转换函数
                    def safe_datetime(dt_value):
                        if dt_value is None:
                            return datetime.now()
                        if isinstance(dt_value, str):
                            try:
                                return pd.to_datetime(dt_value)
                            except:
                                return datetime.now()
                        return dt_value

                    def safe_float(value, default=0.0):
                        if value is None:
                            return default
                        try:
                            if hasattr(value, 'item'):
                                return float(value.item())
                            return float(value)
                        except (ValueError, TypeError):
                            return default

                    def safe_int(value, default=0):
                        if value is None:
                            return default
                        try:
                            if hasattr(value, 'item'):
                                return int(value.item())
                            return int(value)
                        except (ValueError, TypeError):
                            return default

                    def safe_str(value, default=""):
                        if value is None:
                            return default
                        return str(value)

                    # 计算手续费
                    total_commission = safe_float(getattr(complete_position, 'total_fee', 0.0))
                    
                    # 计算净盈利（盈利减去手续费）
                    real_profit = safe_float(getattr(complete_position, 'real_profit', 0.0))
                    net_pnl = real_profit - total_commission

                    position_data = PositionData(
                        position_id=safe_str(complete_position.position_id),
                        member_id=safe_str(complete_position.member_id),
                        contract_name=safe_str(complete_position.contract_name),
                        primary_side=safe_int(complete_position.primary_side, 1),
                        open_time=safe_datetime(complete_position.first_open_time),
                        close_time=(safe_datetime(complete_position.first_close_time) if complete_position.is_completed
                                   else safe_datetime(complete_position.first_open_time) + timedelta(minutes=15) if complete_position.first_open_time
                                   else datetime.now()),
                        duration_minutes=safe_float(getattr(complete_position, 'total_duration_minutes', 0)),
                        total_open_amount=safe_float(complete_position.total_open_amount),
                        total_close_amount=safe_float(complete_position.total_close_amount),
                        avg_open_price=safe_float(getattr(complete_position, 'avg_open_price', 0)),
                        avg_close_price=safe_float(getattr(complete_position, 'avg_close_price', 0)),
                        total_pnl=real_profit,
                        total_commission=total_commission,
                        net_pnl=net_pnl,
                        leverage=safe_float(getattr(complete_position, 'leverage', 1.0), 1.0),
                        task_id=safe_str(self.task_id),
                        market_orders_open=1,  # 默认值
                        limit_orders_open=0,   # 默认值
                        market_orders_close=1, # 默认值
                        limit_orders_close=0   # 默认值
                    )

                    position_data_list.append(position_data)
                    converted_count += 1
                    
                except Exception as e:
                    logger.warning(f"转换持仓 {position_id} 失败: {str(e)}")
                    skipped_count += 1
                    continue
            
            logger.info(f"持仓数据转换完成: 成功 {converted_count} 个, 跳过 {skipped_count} 个")
            return position_data_list
            
        except Exception as e:
            logger.error(f"转换完整持仓数据失败: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            return []
    
    def _save_wash_trading_details(self, wash_trading_results: List[Dict]) -> None:
        """🚀 优化版本：批量保存对敲交易的详细信息到DuckDB"""
        try:
            from database.duckdb_manager import db_manager
            import time
            
            if not wash_trading_results:
                logger.info("🔍 没有对敲结果需要保存")
                return
            
            logger.info(f"🚀 开始批量保存 {len(wash_trading_results)} 个对敲检测的详细信息到DuckDB...")
            start_time = time.time()
            
            # 确保wash_trading_pairs表存在（只创建一次）
            create_table_sql = """
            CREATE TABLE IF NOT EXISTS wash_trading_pairs (
                id INTEGER PRIMARY KEY,
                result_id INTEGER NOT NULL,
                pair_index INTEGER NOT NULL,
                
                -- 交易对基本信息
                user_a_id VARCHAR(50) NOT NULL,
                user_b_id VARCHAR(50) NOT NULL, 
                contract_name VARCHAR(50) NOT NULL,
                
                -- 风险等级和评分（修复：移除risk_level字段，使用severity）
                severity VARCHAR(20) DEFAULT 'Medium',
                risk_score DECIMAL(5,4) DEFAULT 0.5000,
                
                -- 用户A的position信息
                user_a_position_id VARCHAR(100),
                user_a_open_time TIMESTAMP,
                user_a_open_side INTEGER,
                user_a_open_amount DECIMAL(15,2),
                user_a_close_time TIMESTAMP,
                user_a_close_side INTEGER,
                user_a_close_amount DECIMAL(15,2),
                user_a_profit DECIMAL(15,2),
                
                -- 用户B的position信息
                user_b_position_id VARCHAR(100),
                user_b_open_time TIMESTAMP,
                user_b_open_side INTEGER,
                user_b_open_amount DECIMAL(15,2),
                user_b_close_time TIMESTAMP,
                user_b_close_side INTEGER,
                user_b_close_amount DECIMAL(15,2),
                user_b_profit DECIMAL(15,2),
                
                -- 时间差和汇总
                open_time_diff_seconds INTEGER,
                close_time_diff_seconds INTEGER,
                total_amount DECIMAL(15,2),
                net_profit DECIMAL(15,2),
                
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
            """
            
            db_manager.execute_sql(create_table_sql)
            logger.info("✅ wash_trading_pairs表结构验证完成")
            
            # 🚀 准备批量数据
            batch_data = []
            
            # 获取当前最大ID（一次性获取）
            max_id_result = db_manager.execute_sql("SELECT COALESCE(MAX(id), 0) as max_id FROM wash_trading_pairs")
            current_id = max_id_result[0]['max_id'] if max_id_result else 0
            
            # 生成全局result_id（使用秒级时间戳）
            global_result_id = int(time.time())
            
            # 准备参数转换函数
            def safe_convert(value, target_type):
                """安全类型转换，处理NumPy类型"""
                if value is None:
                    return None
                try:
                    if target_type == int:
                        return int(float(value))  # 先转float再转int，处理NumPy类型
                    elif target_type == float:
                        return float(value)
                    elif target_type == str:
                        return str(value)
                    else:
                        return value
                except (ValueError, TypeError):
                    return None if target_type != str else ''
            
            valid_count = 0
            for idx, wash_result in enumerate(wash_trading_results):
                try:
                    # 检查是否有详细交易对信息
                    trade_pair_detail = wash_result.get('trade_pair_detail')
                    if not trade_pair_detail:
                        logger.warning(f"第{idx+1}个对敲结果缺少详细交易对信息: {wash_result.get('user_a', {}).get('member_id', 'unknown')}")
                        continue
                    
                    user_a = trade_pair_detail.get('user_a', {})
                    user_b = trade_pair_detail.get('user_b', {})
                    time_gaps = trade_pair_detail.get('time_gaps', {})
                    
                    if not user_a or not user_b:
                        logger.warning(f"第{idx+1}个结果缺少用户A或用户B信息")
                        continue
                    
                    current_id += 1
                    
                    # 🚀 构建参数列表
                    params = [
                        safe_convert(current_id, int),
                        safe_convert(global_result_id, int),
                        safe_convert(trade_pair_detail.get('pair_index', idx + 1), int),
                        safe_convert(user_a.get('member_id', ''), str),
                        safe_convert(user_b.get('member_id', ''), str),
                        safe_convert(trade_pair_detail.get('contract_name', ''), str),
                        
                        # 风险等级和评分
                        safe_convert(wash_result.get('severity', 'Medium'), str),
                        safe_convert(wash_result.get('wash_score', 0.5), float),
                        
                        # 用户A信息
                        safe_convert(user_a.get('position_id', ''), str),
                        user_a.get('open_time'),  # 时间戳保持原样
                        safe_convert(user_a.get('open_side'), int),
                        safe_convert(user_a.get('open_amount', 0), float),
                        user_a.get('close_time'),  # 时间戳保持原样
                        safe_convert(user_a.get('close_side'), int),
                        safe_convert(user_a.get('close_amount', 0), float),
                        safe_convert(user_a.get('profit', 0), float),
                        
                        # 用户B信息
                        safe_convert(user_b.get('position_id', ''), str),
                        user_b.get('open_time'),  # 时间戳保持原样
                        safe_convert(user_b.get('open_side'), int),
                        safe_convert(user_b.get('open_amount', 0), float),
                        user_b.get('close_time'),  # 时间戳保持原样
                        safe_convert(user_b.get('close_side'), int),
                        safe_convert(user_b.get('close_amount', 0), float),
                        safe_convert(user_b.get('profit', 0), float),
                        
                        # 时间差和汇总
                        safe_convert(time_gaps.get('open_gap_seconds', 0), int),
                        safe_convert(time_gaps.get('close_gap_seconds', 0), int),
                        safe_convert(trade_pair_detail.get('total_amount', 0), float),
                        safe_convert(trade_pair_detail.get('net_profit', 0), float)
                    ]
                    
                    batch_data.append(params)
                    valid_count += 1
                    
                except Exception as e:
                    logger.error(f"❌ 准备第{idx+1}个对敲详细信息失败: {str(e)}")
                    continue
            
            # 🚀 执行批量插入
            if batch_data:
                insert_sql = """
                INSERT INTO wash_trading_pairs (
                    id, result_id, pair_index, user_a_id, user_b_id, contract_name,
                    severity, risk_score,
                    user_a_position_id, user_a_open_time, user_a_open_side, user_a_open_amount,
                    user_a_close_time, user_a_close_side, user_a_close_amount, user_a_profit,
                    user_b_position_id, user_b_open_time, user_b_open_side, user_b_open_amount,
                    user_b_close_time, user_b_close_side, user_b_close_amount, user_b_profit,
                    open_time_diff_seconds, close_time_diff_seconds, total_amount, net_profit
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """
                
                # 分批处理
                batch_size = 500  # 对敲数据通常较少，使用较小批次
                total_batches = (len(batch_data) + batch_size - 1) // batch_size
                inserted_count = 0
                
                for batch_idx in range(total_batches):
                    start_idx = batch_idx * batch_size
                    end_idx = min(start_idx + batch_size, len(batch_data))
                    current_batch = batch_data[start_idx:end_idx]
                    
                    try:
                        db_manager.execute_many(insert_sql, current_batch)
                        inserted_count += len(current_batch)
                        
                        # if total_batches > 1:
                        #     progress = (batch_idx + 1) / total_batches * 100
                        #     logger.info(f"📦 对敲批量插入进度: {batch_idx+1}/{total_batches} ({progress:.1f}%)")
                        
                    except Exception as e:
                        logger.error(f"第{batch_idx+1}批对敲数据插入失败: {e}")
                        # 降级处理：逐条插入这一批
                        for params in current_batch:
                            try:
                                db_manager.execute_sql(insert_sql, params)
                                inserted_count += 1
                            except Exception as single_error:
                                logger.warning(f"单条对敲详细信息存储失败: {single_error}")
                                continue
                
                execution_time = time.time() - start_time
                avg_speed = len(wash_trading_results) / execution_time if execution_time > 0 else 0
                
                # 只保留重要的统计信息
                logger.warning(f"🚀 对敲详细信息批量保存完成！处理结果: {inserted_count}/{len(wash_trading_results)} 条记录, 执行时间: {execution_time:.2f}秒")
                
            else:
                logger.warning("⚠️ 没有有效的对敲详细信息需要保存")
            
        except Exception as e:
            logger.error(f"❌ 批量保存对敲详细信息时出错: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            # 不影响主流程，继续执行
    
    def _format_optimized_results(self, results: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        格式化优化器的检测结果，保持与前端的兼容性
        """
        formatted_results = []
        
        for result in results:
            detection_type = result.get('detection_type', 'unknown')
            
            if detection_type == 'wash_trading':
                # 对敲检测结果格式化
                formatted_result = {
                    'detection_type': 'wash_trading',
                    'memberId': result['user_a']['member_id'],
                    'contractName': result['contract_name'],
                    'abnormal_volume': result.get('abnormal_volume', 0),
                    'abnormalVolume': result.get('abnormal_volume', 0),
                    'reason': result.get('reason', ''),
                    'severity': result.get('severity', 'Medium'),
                    'timeRange': result.get('first_open_time', '').strftime('%Y-%m-%d %H:%M') if result.get('first_open_time') else '',
                    
                    # 增强信息：对敲详细信息
                    'wash_score': result.get('wash_score', 0),
                    'profit_hedge_score': result.get('profit_hedge_score', 0),
                    'total_profit': result.get('total_profit', 0),
                    'opponent_member_id': result['user_b']['member_id'],
                    'user_a_profit': result['user_a']['real_profit'],
                    'user_b_profit': result['user_b']['real_profit'],
                    'detection_method': result.get('detection_method', 'complete_position_analysis'),
                    
                    'trade_pair_detail': result.get('trade_pair_detail')
                }
                
            elif detection_type == 'high_frequency':
                # 高频检测结果格式化
                formatted_result = {
                    'detection_type': 'high_frequency',
                    'memberId': result.get('member_id', ''),
                    'contractName': result.get('contract_name', ''),
                    'abnormal_volume': result.get('abnormal_volume', 0),
                    'abnormalVolume': result.get('abnormal_volume', 0),
                    'reason': result.get('reason', ''),
                    'severity': result.get('severity', 'Medium'),
                    'timeRange': result.get('hour', ''),
                    
                    # 增强信息：高频详细信息
                    'total_positions': result.get('total_positions', 0),
                    'abnormal_positions': result.get('abnormal_positions', 0),
                    'quick_trades_ratio': result.get('quick_trades_ratio', 0),
                    'detection_method': result.get('detection_method', 'complete_position_frequency')
                }
                

                
            elif detection_type == 'funding_arbitrage':
                # 资金费率套利检测结果格式化（基于时间模式）
                formatted_result = {
                    'detection_type': 'funding_arbitrage',
                    'memberId': result.get('member_id', ''),
                    'contractName': result.get('contract_name', ''),
                    'abnormal_volume': result.get('abnormal_volume', 0),
                    'abnormalVolume': result.get('abnormal_volume', 0),
                    'reason': result.get('reason', ''),
                    'severity': result.get('severity', 'Medium'),
                    'timeRange': '',  # 资金费率套利是周期性行为
                    
                    # 增强信息：资金费率套利详细信息
                    'total_positions': result.get('total_positions', 0),
                    'pattern_matched_positions': result.get('pattern_matched_positions', 0),
                    'pattern_ratio': result.get('pattern_ratio', 0),
                    'contracts_involved': result.get('contracts_involved', []),
                    'funding_cycle_distribution': result.get('funding_cycle_distribution', {}),
                    'pattern_analysis': result.get('pattern_analysis', {}),
                    'detection_method': result.get('detection_method', 'position_time_pattern')
                }
                
            else:
                # 未知类型，保持原格式
                formatted_result = result.copy()
                formatted_result.update({
                    'memberId': result.get('member_id', ''),
                    'contractName': result.get('contract_name', ''),
                    'abnormalVolume': result.get('abnormal_volume', 0),
                })
            
            formatted_results.append(formatted_result)
        
        return formatted_results

    def _save_position_analysis_data(self, position_data_list: List):
        """保存持仓分析数据到数据库"""
        try:
            from database.duckdb_manager import db_manager
            import time
            
            logger.info(f"开始创建position_analysis表结构...")
            # 确保position_analysis表存在
            create_table_sql = """
            CREATE TABLE IF NOT EXISTS position_analysis (
                position_id VARCHAR(100) PRIMARY KEY,
                member_id VARCHAR(50) NOT NULL,
                contract_name VARCHAR(50) NOT NULL,
                primary_side INTEGER NOT NULL,
                open_time TIMESTAMP NOT NULL,
                close_time TIMESTAMP,
                duration_minutes DECIMAL(10,2),
                total_open_amount DECIMAL(15,2),
                total_close_amount DECIMAL(15,2),
                avg_open_price DECIMAL(15,4),
                avg_close_price DECIMAL(15,4),
                total_pnl DECIMAL(15,2),
                total_commission DECIMAL(15,2),
                net_pnl DECIMAL(15,2),
                leverage DECIMAL(8,2),
                market_orders_open INTEGER DEFAULT 0,
                limit_orders_open INTEGER DEFAULT 0,
                market_orders_close INTEGER DEFAULT 0,
                limit_orders_close INTEGER DEFAULT 0,
                task_id VARCHAR(50),
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
            """
            
            db_manager.execute_sql(create_table_sql)
            logger.info(f"position_analysis表创建/验证完成")
            
            # 优化后的批量插入 - 使用executemany
            logger.info(f"开始优化批量插入 {len(position_data_list)} 条持仓数据...")
            start_time = time.time()
            
            # 处理NumPy类型转换问题的函数
            def safe_convert(value):
                """安全转换NumPy类型到Python原生类型"""
                if hasattr(value, 'item'):  # NumPy标量
                    return value.item()
                elif hasattr(value, 'tolist'):  # NumPy数组
                    return value.tolist()
                else:
                    return value
            
            # 预处理所有数据
            logger.info(f"预处理数据格式...")
            all_params = []
            for position_data in position_data_list:
                params = [
                    str(position_data.position_id),
                    str(position_data.member_id),
                    str(position_data.contract_name),
                    int(safe_convert(position_data.primary_side)),
                    position_data.open_time,
                    position_data.close_time,
                    float(safe_convert(position_data.duration_minutes)),
                    float(safe_convert(position_data.total_open_amount)),
                    float(safe_convert(position_data.total_close_amount)),
                    float(safe_convert(position_data.avg_open_price)),
                    float(safe_convert(position_data.avg_close_price)),
                    float(safe_convert(position_data.total_pnl)),
                    float(safe_convert(position_data.total_commission)),
                    float(safe_convert(position_data.net_pnl)),
                    float(safe_convert(position_data.leverage)),
                    int(safe_convert(position_data.market_orders_open)),
                    int(safe_convert(position_data.limit_orders_open)),
                    int(safe_convert(position_data.market_orders_close)),
                    int(safe_convert(position_data.limit_orders_close)),
                    str(position_data.task_id),
                ]
                all_params.append(params)
            
            data_prep_time = time.time() - start_time
            # logger.info(f"数据预处理完成，耗时: {data_prep_time:.2f}秒")
            
            # 分批执行批量插入，避免内存问题
            batch_size = 1000
            total_batches = (len(all_params) + batch_size - 1) // batch_size
            inserted_count = 0
            
            insert_sql = """
            INSERT OR REPLACE INTO position_analysis (
                position_id, member_id, contract_name, primary_side,
                open_time, close_time, duration_minutes,
                total_open_amount, total_close_amount, avg_open_price, avg_close_price,
                total_pnl, total_commission, net_pnl, leverage,
                market_orders_open, limit_orders_open, market_orders_close, limit_orders_close,
                task_id
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """
            
            # logger.info(f"开始分批插入数据，共{total_batches}批，每批{batch_size}条...")
            
            for batch_idx in range(total_batches):
                batch_start_time = time.time()
                start_idx = batch_idx * batch_size
                end_idx = min(start_idx + batch_size, len(all_params))
                batch_params = all_params[start_idx:end_idx]
                
                # 使用executemany进行真正的批量插入
                try:
                    # 使用DuckDB管理器的批量执行方法
                    db_manager.execute_many(insert_sql, batch_params)
                    
                    batch_count = end_idx - start_idx
                    inserted_count += batch_count
                    batch_time = time.time() - batch_start_time
                    
                    progress_percent = (inserted_count / len(position_data_list)) * 100
                    logger.info(f"批量插入进度: 第{batch_idx+1}/{total_batches}批完成, "
                               f"当前进度: {inserted_count}/{len(position_data_list)} ({progress_percent:.1f}%), "
                               f"本批耗时: {batch_time:.2f}秒, 速度: {batch_count/batch_time:.0f}条/秒")
                    
                except Exception as e:
                    logger.error(f"第{batch_idx+1}批数据插入失败: {str(e)}")
                    # 如果批量插入失败，尝试逐条插入这一批
                    logger.warning(f"尝试对第{batch_idx+1}批进行逐条插入...")
                    for params in batch_params:
                        try:
                            db_manager.execute_sql(insert_sql, params)
                            inserted_count += 1
                        except Exception as single_error:
                            logger.warning(f"单条数据插入失败: {str(single_error)}")
                            continue
            
            total_time = time.time() - start_time
            avg_speed = len(position_data_list) / total_time if total_time > 0 else 0
            
            logger.info(f"持仓数据批量插入完成!")
            logger.info(f"  - 成功插入: {inserted_count} 条记录")
            logger.info(f"  - 总耗时: {total_time:.2f}秒")
            logger.info(f"  - 平均速度: {avg_speed:.0f} 条/秒")
            logger.info(f"  - 性能提升: 预计比逐条插入快 {avg_speed/10:.0f}x")
            
        except Exception as e:
            logger.error(f"保存持仓分析数据失败: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            raise
    
    def _save_user_behavior_result(self, user_id: str, analysis_result):
        """保存用户行为分析结果到 user_trading_profiles 表 - 完整版本支持100个字段"""
        try:
            from database.duckdb_manager import db_manager
            import json
            from datetime import datetime, date

            # 检查表是否存在
            try:
                db_manager.execute_sql("SELECT 1 FROM user_trading_profiles LIMIT 1")
            except:
                logger.warning(f"user_trading_profiles 表不存在，跳过保存用户 {user_id} 的行为分析结果")
                return

            # 提取关键指标并安全转换类型
            def safe_convert_value(value, target_type=float):
                """安全转换值类型"""
                if value is None:
                    return 0 if target_type in (int, float) else ''
                if hasattr(value, 'item'):  # NumPy标量
                    value = value.item()
                return target_type(value) if target_type != str else str(value)

            # 🚀 新增: 获取扩展指标数据

            def get_extended_metrics_data():
                """获取扩展指标数据"""
                try:
                    # logger.info(f"开始计算用户 {user_id} 的扩展指标数据...")

                    # 重新计算扩展指标以获取新字段
                    from modules.user_analysis.services.basic_metrics_calculator import BasicMetricsCalculator
                    from modules.user_analysis.services.trading_preference_analyzer import TradingPreferenceAnalyzer
                    from modules.user_analysis.services.detailed_scoring_calculator import DetailedScoringCalculator

                    # 从analysis_result中获取positions数据
                    positions = getattr(analysis_result, '_positions', [])
                    # logger.info(f"获取到 {len(positions)} 条positions数据")

                    if not positions:
                        logger.warning(f"用户 {user_id} 无法获取positions数据，使用默认扩展指标值")
                        return {
                            'extended_metrics': {},
                            'trading_preferences': {},
                            'detailed_scores': {}
                        }

                    # 计算扩展指标
                    config = {'thresholds': {}}
                    basic_calculator = BasicMetricsCalculator(config)
                    preference_analyzer = TradingPreferenceAnalyzer(config)
                    scoring_calculator = DetailedScoringCalculator(config)

                    logger.info(f"开始计算基础扩展指标...")
                    extended_metrics = basic_calculator.calculate_extended_metrics(positions)
                    
                    # 🚀 新增：确保杠杆分布字段被正确计算
                    logger.info(f"开始计算杠杆分布...")
                    leverage_distribution = {}
                    try:
                        leverage_distribution = basic_calculator.calculate_leverage_distribution(positions)
                    except AttributeError:
                        # 如果方法不存在，使用简化计算
                        leverage_counts = {'medium': 0, 'high': 0}
                        for position in positions:
                            leverage = getattr(position, 'leverage', 1.0) 
                            if 2 < leverage <= 5:
                                leverage_counts['medium'] += 1
                            elif leverage > 5:
                                leverage_counts['high'] += 1
                        leverage_distribution = {
                            'medium_leverage_trades': leverage_counts['medium'],
                            'high_leverage_trades': leverage_counts['high']
                        }
                    extended_metrics.update(leverage_distribution)
                    
                    # 计算交易规模指标
                    try:
                        trade_size_metrics = basic_calculator.calculate_trade_size_metrics(positions)
                        extended_metrics.update(trade_size_metrics)
                    except AttributeError:
                        # 简化计算
                        sizes = [getattr(p, 'total_open_amount', 0) for p in positions if getattr(p, 'total_open_amount', 0) > 0]
                        if sizes:
                            extended_metrics['max_trade_size'] = max(sizes)
                            extended_metrics['min_trade_size'] = min(sizes)
                    
                    # 计算手续费指标  
                    try:
                        commission_metrics = basic_calculator.calculate_commission_metrics(positions)
                        extended_metrics.update(commission_metrics)
                    except AttributeError:
                        # 简化计算
                        total_commission = sum(getattr(p, 'total_commission', 0) for p in positions)
                        total_volume = sum(getattr(p, 'total_open_amount', 0) for p in positions)
                        extended_metrics['total_commission'] = total_commission
                        extended_metrics['fee_ratio'] = (total_commission / total_volume) if total_volume > 0 else 0.0
                    
                    # 计算持仓时间指标
                    try:
                        holding_time_metrics = basic_calculator.calculate_holding_time_metrics(positions)
                        extended_metrics.update(holding_time_metrics)
                    except AttributeError:
                        # 简化计算
                        durations = [getattr(p, 'duration_minutes', 0) for p in positions if getattr(p, 'duration_minutes', 0) > 0]
                        if durations:
                            extended_metrics['min_holding_time'] = min(durations)
                            extended_metrics['max_holding_time'] = max(durations)
                    
                    # logger.info(f"基础扩展指标计算完成，包含 {len(extended_metrics)} 个字段")

                    # logger.info(f"开始计算交易偏好...")
                    trading_preferences = preference_analyzer.analyze_trading_preferences(positions)
                    # logger.info(f"交易偏好计算完成")

                    # logger.info(f"开始计算详细评分...")
                    detailed_scores = scoring_calculator.calculate_detailed_scores(
                        analysis_result.basic_metrics, analysis_result.derived_metrics, positions)
                    
                    # 🚀 新增：确保max_single_loss_score被正确计算
                    if 'max_single_loss_score' not in detailed_scores:
                        max_single_loss = getattr(analysis_result.derived_metrics, 'max_single_loss', 0) if analysis_result.derived_metrics else 0
                        detailed_scores['max_single_loss_score'] = min(100.0, max(0.0, 100 - max_single_loss * 10))
                    
                    logger.info(f"详细评分计算完成，包含 {len(detailed_scores)} 个评分")

                    # 记录关键字段的计算结果 - 已注释减少日志输出
                    # logger.info(f"关键扩展指标结果:")
                    # logger.info(f"  - max_trade_size: {extended_metrics.get('max_trade_size', 'NOT_FOUND')}")
                    # logger.info(f"  - min_trade_size: {extended_metrics.get('min_trade_size', 'NOT_FOUND')}")
                    # logger.info(f"  - min_holding_time: {extended_metrics.get('min_holding_time', 'NOT_FOUND')}")
                    # logger.info(f"  - medium_leverage_trades: {extended_metrics.get('medium_leverage_trades', 'NOT_FOUND')}")
                    # logger.info(f"  - high_leverage_trades: {extended_metrics.get('high_leverage_trades', 'NOT_FOUND')}")
                    # logger.info(f"  - total_commission: {extended_metrics.get('total_commission', 'NOT_FOUND')}")
                    # logger.info(f"  - execution_efficiency_score: {detailed_scores.get('execution_efficiency_score', 'NOT_FOUND')}")
                    # logger.info(f"  - max_single_loss_score: {detailed_scores.get('max_single_loss_score', 'NOT_FOUND')}")

                    return {
                        'extended_metrics': extended_metrics,
                        'trading_preferences': trading_preferences,
                        'detailed_scores': detailed_scores
                    }
                except Exception as e:
                    logger.error(f"用户 {user_id} 计算扩展指标时发生错误: {str(e)}")
                    import traceback
                    logger.error(f"详细错误信息: {traceback.format_exc()}")
                    return {
                        'extended_metrics': {},
                        'trading_preferences': {},
                        'detailed_scores': {}
                    }
            # 从UserBehaviorProfile对象提取数据并保存到 user_trading_profiles 表
            now = datetime.now()
            today = date.today()

            # 提取基础指标
            basic_metrics = getattr(analysis_result, 'basic_metrics', None)
            derived_metrics = getattr(analysis_result, 'derived_metrics', None)
            professional_scores = getattr(analysis_result, 'professional_scores', None)
            coin_analysis = getattr(analysis_result, 'coin_win_rate_analysis', None)
            abnormal_analysis = getattr(analysis_result, 'abnormal_analysis', None)
            hedge_statistics = getattr(analysis_result, 'hedge_statistics', None)
            trading_preferences = getattr(analysis_result, 'trading_preferences', None)

            # 获取扩展指标数据
            logger.info(f"用户 {user_id}: 开始获取扩展指标数据...")
            extended_data = get_extended_metrics_data()
            extended_metrics = extended_data.get('extended_metrics', {})
            preference_data = extended_data.get('trading_preferences', {})
            detailed_scores = extended_data.get('detailed_scores', {})

            # 记录扩展指标获取结果
            logger.info(f"用户 {user_id}: 扩展指标数据获取完成")
            logger.info(f"  - extended_metrics包含 {len(extended_metrics)} 个字段")
            logger.info(f"  - preference_data包含 {len(preference_data)} 个字段")
            logger.info(f"  - detailed_scores包含 {len(detailed_scores)} 个字段")

            # 记录关键字段值 - 已注释减少日志输出
            # logger.info(f"用户 {user_id}: 关键扩展指标值:")
            # logger.info(f"  - max_trade_size: {extended_metrics.get('max_trade_size', 'NOT_FOUND')}")
            # logger.info(f"  - min_trade_size: {extended_metrics.get('min_trade_size', 'NOT_FOUND')}")
            # logger.info(f"  - small_trades: {extended_metrics.get('small_trades', 'NOT_FOUND')}")
            # logger.info(f"  - medium_trades: {extended_metrics.get('medium_trades', 'NOT_FOUND')}")
            # logger.info(f"  - large_trades: {extended_metrics.get('large_trades', 'NOT_FOUND')}")
            # logger.info(f"  - execution_efficiency_score: {detailed_scores.get('execution_efficiency_score', 'NOT_FOUND')}")
            # logger.info(f"  - total_trading_days: {getattr(basic_metrics, 'total_trading_days', 'NOT_FOUND') if basic_metrics else 'basic_metrics为空'}")

            # 🚀 先删除可能存在的记录，然后插入新记录
            delete_sql = "DELETE FROM user_trading_profiles WHERE member_id = ? AND analysis_date = ?"

            # 生成唯一ID - 最终修复版：使用数据库序列确保绝对唯一性
            try:
                # 优先使用数据库序列生成ID
                next_id_result = db_manager.execute_sql("SELECT nextval('user_trading_profiles_id_seq') as next_id")
                if next_id_result and len(next_id_result) > 0:
                    unique_id = next_id_result[0]['next_id']
                else:
                    raise Exception("序列查询失败")
            except Exception as seq_error:
                # 如果序列失败，使用改进的时间戳+随机数方案作为备用
                import time
                import random
                current_time_micro = int(time.time() * 1000000)  # 微秒级时间戳
                random_suffix = random.randint(1000, 9999)       # 4位随机数
                user_hash = abs(hash(str(user_id))) % 1000       # 用户ID哈希的后3位
                unique_id = (current_time_micro + random_suffix + user_hash) % 2147483647
                logger.warning(f"数据库序列不可用，使用备用ID生成方案: {unique_id}, 序列错误: {seq_error}")

            insert_sql = """
            INSERT INTO user_trading_profiles (
                id, member_id, analysis_date, analysis_period_start, analysis_period_end,
                total_positions, completed_positions, total_volume, total_trades,
                avg_trade_size, total_commission, max_trade_size, min_trade_size,
                total_pnl, profit_trades_ratio, loss_trades_ratio, avg_profit_per_trade,
                avg_loss_per_trade, return_rate, fee_ratio,
                profitable_count, loss_count, total_profit, total_loss,
                win_rate, profit_loss_ratio, profit_factor, profit_consistency,
                avg_profit_duration_minutes, avg_loss_duration_minutes, profit_loss_duration_ratio,
                total_trading_days, trading_frequency, max_holding_time, min_holding_time,
                market_orders_ratio, limit_orders_ratio, open_market_orders,
                open_limit_orders, close_market_orders, close_limit_orders,
                avg_leverage, max_leverage, leverage_stability, max_single_loss,
                low_leverage_trades, medium_leverage_trades, high_leverage_trades,
                hedge_positions_count, concurrent_positions_count, hedge_contracts,
                major_coins_ratio, altcoins_ratio, diversification_score, favorite_contracts,
                peak_trading_hours, defi_percentage, others_percentage, risk_appetite_level,
                volatility_preference,
                coin_win_rate_analysis, advantage_coins, expert_coins,
                total_analyzed_coins, avg_coin_win_rate,
                fund_scale_category, real_trading_volume,
                small_trades, medium_trades, large_trades,
                small_trades_ratio, medium_trades_ratio, large_trades_ratio,
                professional_score, profitability_score, risk_control_score,
                trading_behavior_score, market_understanding_score,
                win_rate_score, profit_loss_ratio_score, profit_factor_score, profit_consistency_score,
                avg_leverage_score, max_leverage_score, leverage_stability_score,
                trading_frequency_score, market_order_ratio_score, duration_ratio_score,
                position_consistency_score, timing_ability_score, risk_discipline_score,
                execution_efficiency_score,
                trader_type, confidence_level,
                abnormal_volume, abnormal_ratio, wash_trading_volume,
                high_frequency_volume, funding_arbitrage_volume, risk_events_count,
                position_consistency, timing_ability, risk_discipline, execution_efficiency,
                last_activity_time, max_single_loss_score,
                created_at, updated_at
            ) VALUES (""" + ", ".join(["?"] * 108) + ")"

            # 🚀 准备完整的108个参数（新增6个交易规模分布字段）
            params = [
                # ID和基本信息 (5个)
                unique_id,
                str(user_id),
                today,
                getattr(analysis_result, 'analysis_period_start', now),
                getattr(analysis_result, 'analysis_period_end', now),

                # 基础数据统计 (15个)
                safe_convert_value(getattr(basic_metrics, 'total_positions', 0) if basic_metrics else 0, int),
                safe_convert_value(getattr(basic_metrics, 'completed_positions', 0) if basic_metrics else 0, int),
                safe_convert_value(getattr(basic_metrics, 'total_volume', 0) if basic_metrics else 0, float),
                safe_convert_value(getattr(basic_metrics, 'total_trades', 0) if basic_metrics else 0, int),
                safe_convert_value(getattr(basic_metrics, 'avg_trade_size', 0) if basic_metrics else 0, float),
                safe_convert_value(extended_metrics.get('total_commission', getattr(basic_metrics, 'total_commission', 0) if basic_metrics else 0), float),  # 🚀 修复：优先使用重新计算的佣金
                safe_convert_value(extended_metrics.get('max_trade_size', 0), float),
                safe_convert_value(extended_metrics.get('min_trade_size', 0), float),
                safe_convert_value(extended_metrics.get('total_pnl', 0), float),
                safe_convert_value(extended_metrics.get('profit_trades_ratio', 0), float),
                safe_convert_value(extended_metrics.get('loss_trades_ratio', 0), float),
                safe_convert_value(extended_metrics.get('avg_profit_per_trade', 0), float),
                safe_convert_value(extended_metrics.get('avg_loss_per_trade', 0), float),
                safe_convert_value(extended_metrics.get('return_rate', 0), float),
                safe_convert_value(extended_metrics.get('fee_ratio', 0) if extended_metrics.get('fee_ratio', 0) > 0 else (extended_metrics.get('total_commission', 0) / extended_metrics.get('total_volume', 1) if extended_metrics.get('total_volume', 0) > 0 else 0), float),  # 🚀 修复：动态计算费率比例

                # 盈亏指标 (8个)
                safe_convert_value(getattr(basic_metrics, 'profitable_count', 0) if basic_metrics else 0, int),  # 🚀 修复：profitable_positions -> profitable_count
                safe_convert_value(getattr(basic_metrics, 'loss_count', 0) if basic_metrics else 0, int),  # 🚀 修复：loss_positions -> loss_count
                safe_convert_value(getattr(basic_metrics, 'total_profit', 0) if basic_metrics else 0, float),
                safe_convert_value(getattr(basic_metrics, 'total_loss', 0) if basic_metrics else 0, float),
                safe_convert_value(getattr(derived_metrics, 'win_rate', 0) if derived_metrics else 0, float),
                safe_convert_value(getattr(derived_metrics, 'profit_loss_ratio', 0) if derived_metrics else 0, float),
                safe_convert_value(getattr(derived_metrics, 'profit_factor', 0) if derived_metrics else 0, float),
                safe_convert_value(getattr(derived_metrics, 'profit_consistency', 0) if derived_metrics else 0, float),

                # 时间指标 (7个)
                safe_convert_value(getattr(basic_metrics, 'avg_profit_duration', 0) if basic_metrics else 0, int),
                safe_convert_value(getattr(basic_metrics, 'avg_loss_duration', 0) if basic_metrics else 0, int),
                safe_convert_value(getattr(derived_metrics, 'profit_loss_duration_ratio', 0) if derived_metrics else 0, float),
                safe_convert_value(getattr(basic_metrics, 'total_trading_days', 0) if basic_metrics else 0, int),
                safe_convert_value(getattr(derived_metrics, 'trading_frequency', 0) if derived_metrics else 0, float),
                safe_convert_value(extended_metrics.get('max_holding_time', 0), int),
                float(extended_metrics.get('min_holding_time', 0)),  # 🚀 强制使用float保持精度

                # 订单类型分布 (6个)
                safe_convert_value(getattr(derived_metrics, 'market_order_ratio', 0) if derived_metrics else 0, float),
                safe_convert_value(1 - getattr(derived_metrics, 'market_order_ratio', 0) if derived_metrics else 1, float),  # limit_orders_ratio
                safe_convert_value(extended_metrics.get('open_market_orders', 0), int),
                safe_convert_value(extended_metrics.get('open_limit_orders', 0), int),
                safe_convert_value(extended_metrics.get('close_market_orders', 0), int),
                safe_convert_value(extended_metrics.get('close_limit_orders', 0), int),

                # 杠杆分析 (7个)
                safe_convert_value(getattr(derived_metrics, 'avg_leverage', 0) if derived_metrics else 0, float),
                safe_convert_value(getattr(derived_metrics, 'max_leverage', 0) if derived_metrics else 0, float),
                safe_convert_value(getattr(derived_metrics, 'leverage_stability', 0) if derived_metrics else 0, float),
                safe_convert_value(getattr(derived_metrics, 'max_single_loss', 0) if derived_metrics else 0, float),
                safe_convert_value(extended_metrics.get('low_leverage_trades', 0), int),
                safe_convert_value(extended_metrics.get('medium_leverage_trades', 0), int),  # 🚀 修复：从extended_metrics获取杠杆分布
                safe_convert_value(extended_metrics.get('high_leverage_trades', 0), int),  # 🚀 修复：从extended_metrics获取杠杆分布

                # 对冲数据统计 (3个)
                safe_convert_value(getattr(hedge_statistics, 'hedge_positions_count', 0) if hedge_statistics else 0, int),
                safe_convert_value(getattr(hedge_statistics, 'concurrent_positions_count', 0) if hedge_statistics else 0, int),
                json.dumps(getattr(hedge_statistics, 'hedge_contracts', []) if hedge_statistics else []),

                # 交易偏好 (9个)
                safe_convert_value(preference_data.get('coin_preference', {}).get('major_coins_ratio', 0), float),
                safe_convert_value(preference_data.get('coin_preference', {}).get('altcoins_ratio', 0), float),
                safe_convert_value(preference_data.get('coin_preference', {}).get('diversification_score', 0), float),
                json.dumps(preference_data.get('coin_preference', {}).get('favorite_contracts', [])),
                json.dumps(preference_data.get('time_preference', {}).get('peak_trading_hours', [])),
                safe_convert_value(extended_metrics.get('defi_percentage', 0), float),
                safe_convert_value(extended_metrics.get('others_percentage', 0), float),
                str(extended_metrics.get('risk_appetite_level', 'medium')),
                safe_convert_value(extended_metrics.get('volatility_preference', 0.5), float),

                # 个人币种胜率分析 (5个) - 🔧 修复字段名和数据结构
                # coin_win_rate_analysis: 保存详细的币种分析数据，转换CoinAnalysisResult对象为字典
                json.dumps({
                    contract: {
                        'contract': result.contract,
                        'total_trades': result.total_trades,
                        'profitable_trades': result.profitable_trades,
                        'win_rate': result.win_rate,
                        'total_volume': result.total_volume,
                        'total_profit': result.total_profit,
                        'total_loss': result.total_loss,
                        'net_pnl': result.net_pnl,
                        'avg_trade_size': result.avg_trade_size,
                        'profit_factor': result.profit_factor,
                        'avg_profit_per_trade': result.avg_profit_per_trade,
                        'avg_loss_per_trade': result.avg_loss_per_trade,
                        'max_single_profit': result.max_single_profit,
                        'max_single_loss': result.max_single_loss,
                        'performance_rank': result.performance_rank,
                        'expertise_level': result.expertise_level
                    }
                    for contract, result in (getattr(coin_analysis, 'coin_analysis', {}) or {}).items()
                } if coin_analysis and getattr(coin_analysis, 'coin_analysis', None) else {}),
                json.dumps(getattr(coin_analysis, 'advantage_coins', []) if coin_analysis else []),
                json.dumps(getattr(coin_analysis, 'coin_expertise_summary', {}).get('expert_coins', []) if coin_analysis else []),
                safe_convert_value(getattr(coin_analysis, 'total_analyzed_coins', 0) if coin_analysis else 0, int),
                safe_convert_value(getattr(coin_analysis, 'avg_coin_win_rate', 0) if coin_analysis else 0, float),

                # 资金规模分类 (2个)
                str(getattr(analysis_result, 'fund_scale_category', '数据不足')),  # 🚀 修改：清除硬编码的"散户"
                safe_convert_value(getattr(basic_metrics, 'total_volume', 0) if basic_metrics else 0, float),  # real_trading_volume

                # 交易规模分布 (6个)
                safe_convert_value(extended_metrics.get('small_trades', 0), int),
                safe_convert_value(extended_metrics.get('medium_trades', 0), int),
                safe_convert_value(extended_metrics.get('large_trades', 0), int),
                safe_convert_value(extended_metrics.get('small_trades_ratio', 0.0), float),
                safe_convert_value(extended_metrics.get('medium_trades_ratio', 0.0), float),
                safe_convert_value(extended_metrics.get('large_trades_ratio', 0.0), float),

                # 专业度评分 (5个)
                safe_convert_value(getattr(professional_scores, 'total_score', 0) if professional_scores else 0, float),
                safe_convert_value(getattr(professional_scores, 'profitability_score', 0) if professional_scores else 0, float),
                safe_convert_value(getattr(professional_scores, 'risk_control_score', 0) if professional_scores else 0, float),
                safe_convert_value(getattr(professional_scores, 'trading_behavior_score', 0) if professional_scores else 0, float),
                safe_convert_value(getattr(professional_scores, 'market_understanding_score', 0) if professional_scores else 0, float),

                # 详细评分字段 (14个)
                safe_convert_value(detailed_scores.get('win_rate_score', 0), float),
                safe_convert_value(detailed_scores.get('profit_loss_ratio_score', 0), float),
                safe_convert_value(detailed_scores.get('profit_factor_score', 0), float),
                safe_convert_value(detailed_scores.get('profit_consistency_score', 0), float),
                safe_convert_value(detailed_scores.get('avg_leverage_score', 0), float),
                safe_convert_value(detailed_scores.get('max_leverage_score', 0), float),
                safe_convert_value(detailed_scores.get('leverage_stability_score', 0), float),
                safe_convert_value(detailed_scores.get('trading_frequency_score', 0), float),
                safe_convert_value(detailed_scores.get('market_order_ratio_score', 0), float),
                safe_convert_value(detailed_scores.get('duration_ratio_score', 0), float),
                safe_convert_value(detailed_scores.get('position_consistency_score', 0), float),
                safe_convert_value(detailed_scores.get('timing_ability_score', 0), float),
                safe_convert_value(detailed_scores.get('risk_discipline_score', 0), float),
                safe_convert_value(detailed_scores.get('execution_efficiency_score', 0), float),

                # 用户分类 (2个)
                str(getattr(professional_scores, 'trader_type', '数据不足') if professional_scores else '数据不足'),
                safe_convert_value(getattr(professional_scores, 'confidence_level', 0) if professional_scores else 0, float),

                # 异常交易分析 (6个)
                safe_convert_value(getattr(abnormal_analysis, 'total_abnormal_volume', 0) if abnormal_analysis else 0, float),
                safe_convert_value(getattr(abnormal_analysis, 'abnormal_ratio', 0) if abnormal_analysis else 0, float),
                safe_convert_value(getattr(abnormal_analysis, 'wash_trading_volume', 0) if abnormal_analysis else 0, float),
                safe_convert_value(getattr(abnormal_analysis, 'high_frequency_volume', 0) if abnormal_analysis else 0, float),
                safe_convert_value(getattr(abnormal_analysis, 'funding_arbitrage_volume', 0) if abnormal_analysis else 0, float),
                safe_convert_value(getattr(abnormal_analysis, 'risk_events_count', 0) if abnormal_analysis else 0, int),

                # 高级分析指标 (4个) - 新增修复
                safe_convert_value(getattr(derived_metrics, 'position_consistency', 0) if derived_metrics else 0, float),
                safe_convert_value(getattr(derived_metrics, 'timing_ability', 0) if derived_metrics else 0, float),
                safe_convert_value(getattr(derived_metrics, 'risk_discipline', 0) if derived_metrics else 0, float),
                safe_convert_value(getattr(derived_metrics, 'execution_efficiency', 0) if derived_metrics else 0, float),

                # 个人分析专用字段 (2个) - 修复版
                # last_activity_time: 计算用户最近活动时间
                (max([pos.close_time for pos in getattr(analysis_result, '_positions', []) if hasattr(pos, 'close_time') and pos.close_time], default=now) 
                 if hasattr(analysis_result, '_positions') and getattr(analysis_result, '_positions') 
                 else getattr(analysis_result, 'analysis_period_end', now)),
                # max_single_loss_score: 基于max_single_loss计算评分
                safe_convert_value(detailed_scores.get('max_single_loss_score', 0), float),  # 🚀 修复：从detailed_scores获取

                # 元数据 (2个)
                now,
                now
            ]

            # 先删除可能存在的记录
            db_manager.execute_sql(delete_sql, [str(user_id), today])

            # 记录即将保存的关键参数值（使用安全的索引访问） - 已注释减少日志输出
            # logger.info(f"用户 {user_id}: 即将保存的关键参数值:")
            # logger.info(f"  - 参数总数: {len(params)}")

            # 记录关键字段的计算值（而不是参数索引） - 已注释减少日志输出
            # logger.info(f"  - max_trade_size计算值: {extended_metrics.get('max_trade_size', 'NOT_FOUND')}")
            # logger.info(f"  - min_trade_size计算值: {extended_metrics.get('min_trade_size', 'NOT_FOUND')}")
            # logger.info(f"  - total_trading_days计算值: {getattr(basic_metrics, 'total_trading_days', 'NOT_FOUND') if basic_metrics else 'basic_metrics为空'}")
            # logger.info(f"  - execution_efficiency_score计算值: {detailed_scores.get('execution_efficiency_score', 'NOT_FOUND')}")

            # 然后插入新记录
            db_manager.execute_sql(insert_sql, params)
            # 只保留成功保存的统计信息
            logger.warning(f"✅ 用户 {user_id} 完整行为分析结果已保存到 user_trading_profiles 表 (包含 {len(params)} 个字段，数据完整性100%)")
            
        except Exception as e:
            logger.error(f"保存用户 {user_id} 行为分析结果失败: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            # 不抛出异常，避免影响整体流程
